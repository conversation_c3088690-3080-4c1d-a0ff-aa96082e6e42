"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const fs_1 = __importDefault(require("fs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
// Apple API Key 信息
const ISSUER_ID = "b4c2ec53-d05c-4928-9a65-f1ae1f325eb4";
const KEY_ID = "62S22U8VM3";
const PRIVATE_KEY_PATH = "AuthKey_62S22U8VM3.p8"; // Apple 下载的私钥
// 生成 JWT token
function generateToken() {
    const privateKey = fs_1.default.readFileSync(PRIVATE_KEY_PATH, "utf8");
    const token = jsonwebtoken_1.default.sign({
        iss: ISSUER_ID,
        exp: Math.floor(Date.now() / 1000) + 1200, // 20分钟有效期
        aud: "appstoreconnect-v1",
    }, privateKey, {
        algorithm: "ES256",
        header: {
            kid: KEY_ID,
            typ: "JWT",
        },
    });
    return token;
}
// 获取设备列表（包含 UUID/UDID）
async function getDevices() {
    const token = generateToken();
    const url = "https://api.appstoreconnect.apple.com/v1/devices";
    try {
        const response = await axios_1.default.get(url, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });
        const devices = response.data.data;
        for (const d of devices) {
            console.log(`Name: ${d.attributes.name}, UDID: ${d.attributes.udid}, Platform: ${d.attributes.platform}, Status: ${d.attributes.status}`);
        }
    }
    catch (err) {
        console.error("Error:", err.response?.status, err.response?.data || err.message);
    }
}
// 入口
getDevices();
